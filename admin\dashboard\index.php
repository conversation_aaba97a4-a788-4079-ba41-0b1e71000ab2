<?php
require_once '../../config/config.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || !isset($_SESSION['is_admin']) || !$_SESSION['is_admin']) {
    redirect('../login.php');
}

$page_title = 'Admin Dashboard';

// Get dashboard statistics
try {
    $db = getDB();
    
    // Get user statistics
    $total_users = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0")->fetch_assoc()['count'];
    $active_users = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND status = 'active'")->fetch_assoc()['count'];
    $pending_kyc = $db->query("SELECT COUNT(*) as count FROM accounts WHERE is_admin = 0 AND kyc_status = 'pending'")->fetch_assoc()['count'];
    
    // Get transaction statistics
    $today_transactions = $db->query("SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as volume FROM transfers WHERE DATE(created_at) = CURDATE() AND status = 'completed'")->fetch_assoc();
    $monthly_transactions = $db->query("SELECT COUNT(*) as count, COALESCE(SUM(amount), 0) as volume FROM transfers WHERE MONTH(created_at) = MONTH(CURDATE()) AND YEAR(created_at) = YEAR(CURDATE()) AND status = 'completed'")->fetch_assoc();
    
    // Get recent users
    $recent_users = $db->query("SELECT id, first_name, last_name, username, email, status, created_at FROM accounts WHERE is_admin = 0 ORDER BY created_at DESC LIMIT 5");
    
    // Get recent transactions
    $recent_transactions = $db->query("SELECT t.*, a.first_name, a.last_name FROM transfers t LEFT JOIN accounts a ON t.sender_id = a.id ORDER BY t.created_at DESC LIMIT 5");
    
} catch (Exception $e) {
    error_log("Dashboard stats error: " . $e->getMessage());
    $total_users = $active_users = $pending_kyc = 0;
    $today_transactions = $monthly_transactions = ['count' => 0, 'volume' => 0];
    $recent_users = $recent_transactions = null;
}

include '../../includes/admin_header.php';
?>

<link rel="stylesheet" href="../../assets/admin/admin.css">
<link rel="stylesheet" href="style.css">



<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        Admin Dashboard
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="../users/" class="btn btn-primary d-none d-sm-inline-block">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <circle cx="9" cy="7" r="4"/>
                                <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                            </svg>
                            Manage Users
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <!-- Statistics Cards -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-sm-6 col-lg-3">
                    <div class="card admin-stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Total Users</div>
                                <div class="ms-auto lh-1">
                                    <a href="../users/" class="text-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="9" cy="7" r="4"/>
                                            <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                            <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                        </svg>
                                    </a>
                                </div>
                            </div>
                            <div class="h1 mb-3" data-stat="total_users"><?php echo number_format($total_users); ?></div>
                            <div class="d-flex mb-2">
                                <div>Registered accounts</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card admin-stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Active Users</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M5 12l5 5l10 -10"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3" data-stat="active_users"><?php echo number_format($active_users); ?></div>
                            <div class="d-flex mb-2">
                                <div>Currently active</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card admin-stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Today's Transactions</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                            <polyline points="9 15 12 12 15 15"/>
                                            <polyline points="12 12 12 21"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3" data-stat="today_transactions"><?php echo number_format($today_transactions['count']); ?></div>
                            <div class="d-flex mb-2">
                                <div><?php echo formatCurrency($today_transactions['volume']); ?> volume</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card admin-stat-card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Pending KYC</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-red">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <line x1="12" y1="8" x2="12" y2="12"/>
                                            <line x1="12" y1="16" x2="12.01" y2="16"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3" data-stat="pending_kyc"><?php echo number_format($pending_kyc); ?></div>
                            <div class="d-flex mb-2">
                                <div>Awaiting verification</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row row-cards mb-3">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Quick Actions</h3>
                        </div>
                        <div class="card-body">
                            <div class="admin-quick-actions">
                                <a href="../users/add.php" class="quick-action-item">
                                    <div class="quick-action-icon admin-stat-icon bg-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="9" cy="7" r="4"/>
                                            <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                            <line x1="19" y1="8" x2="19" y2="14"/>
                                            <line x1="22" y1="11" x2="16" y2="11"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-weight-medium">Add New User</div>
                                        <div class="text-muted">Create a new user account</div>
                                    </div>
                                </a>
                                
                                <a href="../users/" class="quick-action-item">
                                    <div class="quick-action-icon admin-stat-icon bg-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <circle cx="9" cy="7" r="4"/>
                                            <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                            <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                            <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-weight-medium">Manage Users</div>
                                        <div class="text-muted">View and edit user accounts</div>
                                    </div>
                                </a>
                                
                                <a href="../transactions/" class="quick-action-item">
                                    <div class="quick-action-icon admin-stat-icon bg-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                            <polyline points="9 15 12 12 15 15"/>
                                            <polyline points="12 12 12 21"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-weight-medium">View Transactions</div>
                                        <div class="text-muted">Monitor all system transactions</div>
                                    </div>
                                </a>
                                
                                <a href="../settings/" class="quick-action-item">
                                    <div class="quick-action-icon admin-stat-icon bg-purple">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M10.325 4.317c.426 -1.756 2.924 -1.756 3.35 0a1.724 1.724 0 0 0 2.573 1.066c1.543 -.94 3.31 .826 2.37 2.37a1.724 1.724 0 0 0 1.065 2.572c1.756 .426 1.756 2.924 0 3.35a1.724 1.724 0 0 0 -1.066 2.573c.94 1.543 -.826 3.31 -2.37 2.37a1.724 1.724 0 0 0 -2.572 1.065c-.426 1.756 -2.924 1.756 -3.35 0a1.724 1.724 0 0 0 -2.573 -1.066c-1.543 .94 -3.31 -.826 -2.37 -2.37a1.724 1.724 0 0 0 -1.065 -2.572c-1.756 -.426 -1.756 -2.924 0 -3.35a1.724 1.724 0 0 0 1.066 -2.573c-.94 -1.543 .826 -3.31 2.37 -2.37c1 .608 2.296 .07 2.572 -1.065z"/>
                                            <circle cx="12" cy="12" r="3"/>
                                        </svg>
                                    </div>
                                    <div>
                                        <div class="font-weight-medium">System Settings</div>
                                        <div class="text-muted">Configure system preferences</div>
                                    </div>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Activity -->
            <div class="row row-cards">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent User Registrations</h3>
                            <div class="card-actions">
                                <a href="../users/" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($recent_users && $recent_users->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Status</th>
                                            <th>Registered</th>
                                            <th></th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($user = $recent_users->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <span class="user-avatar"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></div>
                                                        <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                                                    </div>
                                                </div>
                                            </td>
                                            <td><?php echo htmlspecialchars($user['email']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $user['status']; ?>"><?php echo ucfirst($user['status']); ?></span>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo formatDate($user['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <a href="../users/edit.php?id=<?php echo $user['id']; ?>" class="btn btn-outline-primary btn-sm">Edit</a>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <p class="empty-title">No recent registrations</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Transactions</h3>
                            <div class="card-actions">
                                <a href="../transactions/" class="btn btn-outline-primary btn-sm">View All</a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>From</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Date</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                        <tr>
                                            <td>
                                                <div class="font-weight-medium"><?php echo htmlspecialchars($transaction['first_name'] . ' ' . $transaction['last_name']); ?></div>
                                                <div class="text-muted"><?php echo htmlspecialchars($transaction['recipient_name']); ?></div>
                                            </td>
                                            <td class="font-weight-medium"><?php echo formatCurrency($transaction['amount']); ?></td>
                                            <td>
                                                <span class="status-badge <?php echo $transaction['status']; ?>"><?php echo ucfirst($transaction['status']); ?></span>
                                            </td>
                                            <td class="text-muted">
                                                <?php echo formatDate($transaction['created_at'], 'M d, H:i'); ?>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <p class="empty-title">No recent transactions</p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="../../assets/admin/admin.js"></script>
<script src="script.js"></script>

<?php include '../../includes/footer.php'; ?>
