<?php
require_once '../config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']) {
        redirect('../admin/');
    } else {
        redirect('../dashboard/');
    }
}

$error = '';
$username = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // Check for too many failed attempts
    $failed_attempts = getFailedLoginAttempts($username, $ip_address);
    if ($failed_attempts >= MAX_LOGIN_ATTEMPTS) {
        $error = 'Too many failed login attempts. Please try again later.';
    } else {
        if (empty($username) || empty($password)) {
            $error = 'Please enter both username and password.';
        } else {
            try {
                $db = getDB();
                $sql = "SELECT id, username, password, first_name, last_name, email, account_number,
                              balance, status, is_admin, kyc_status
                        FROM accounts
                        WHERE username = ? AND status = 'active' AND is_admin = 0";
                
                $result = $db->query($sql, [$username]);
                
                if ($result && $result->num_rows === 1) {
                    $user = $result->fetch_assoc();
                    
                    if (verifyPassword($password, $user['password'])) {
                        // Successful login
                        $_SESSION['user_id'] = $user['id'];
                        $_SESSION['username'] = $user['username'];
                        $_SESSION['first_name'] = $user['first_name'];
                        $_SESSION['last_name'] = $user['last_name'];
                        $_SESSION['email'] = $user['email'];
                        $_SESSION['account_number'] = $user['account_number'];
                        $_SESSION['balance'] = $user['balance'];
                        $_SESSION['is_admin'] = (bool)$user['is_admin'];
                        $_SESSION['kyc_status'] = $user['kyc_status'];
                        $_SESSION['last_activity'] = time();
                        
                        // Record successful login
                        recordLoginAttempt($username, true);
                        
                        // Update last login time
                        $db->query("UPDATE accounts SET last_login = NOW() WHERE id = ?", [$user['id']]);
                        
                        // Log activity
                        logActivity($user['id'], 'User logged in');

                        // Redirect to user dashboard
                        redirect('../dashboard/');
                    } else {
                        $error = 'Invalid username or password.';
                        recordLoginAttempt($username, false);
                    }
                } else {
                    $error = 'Invalid username or password.';
                    recordLoginAttempt($username, false);
                }
            } catch (Exception $e) {
                error_log("Login error: " . $e->getMessage());
                $error = 'An error occurred. Please try again.';
            }
        }
    }
}

$page_title = 'Login';
?>
<!doctype html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <title><?php echo $page_title . ' - ' . APP_NAME; ?></title>
    
    <!-- CSS files -->
    <link href="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/css/tabler.min.css" rel="stylesheet"/>
    <style>
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .card-login {
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            border: none;
        }
        .brand-logo {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
        }
    </style>
</head>
<body class="d-flex flex-column login-page">
    <script src="https://cdn.jsdelivr.net/npm/@tabler/core@1.3.2/dist/js/tabler.min.js"></script>
    
    <div class="page page-center">
        <div class="container container-tight py-4">
            <div class="text-center mb-4">
                <div class="brand-logo">
                    <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                        <path d="M3 21l18 0"/>
                        <path d="M3 10l18 0"/>
                        <path d="M5 6l7 -3l7 3"/>
                        <path d="M4 10l0 11"/>
                        <path d="M20 10l0 11"/>
                        <path d="M8 14l0 3"/>
                        <path d="M12 14l0 3"/>
                        <path d="M16 14l0 3"/>
                    </svg>
                </div>
                <h1 class="text-white mb-1"><?php echo APP_NAME; ?></h1>
                <p class="text-white-50">Secure Online Banking Platform</p>
            </div>
            
            <div class="card card-md card-login">
                <div class="card-body">
                    <h2 class="h2 text-center mb-4">Customer Login</h2>
                    
                    <?php if (!empty($error)): ?>
                    <div class="alert alert-danger" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="9"/>
                                    <line x1="15" y1="9" x2="9" y2="15"/>
                                    <line x1="9" y1="9" x2="15" y2="15"/>
                                </svg>
                            </div>
                            <div><?php echo htmlspecialchars($error); ?></div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <?php if (isset($_GET['timeout'])): ?>
                    <div class="alert alert-warning" role="alert">
                        <div class="d-flex">
                            <div>
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon alert-icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="12" r="9"/>
                                    <polyline points="12 7 12 12 15 15"/>
                                </svg>
                            </div>
                            <div>Your session has expired. Please log in again.</div>
                        </div>
                    </div>
                    <?php endif; ?>
                    
                    <form action="" method="post" autocomplete="off" novalidate>
                        <div class="mb-3">
                            <label class="form-label">Username</label>
                            <input type="text" name="username" class="form-control" placeholder="Enter username" 
                                   value="<?php echo htmlspecialchars($username); ?>" required autocomplete="username">
                        </div>
                        <div class="mb-2">
                            <label class="form-label">
                                Password
                                <span class="form-label-description">
                                    <a href="forgot-password.php">I forgot password</a>
                                </span>
                            </label>
                            <div class="input-group input-group-flat">
                                <input type="password" name="password" class="form-control" placeholder="Enter password" 
                                       required autocomplete="current-password">
                                <span class="input-group-text">
                                    <a href="#" class="link-secondary" title="Show password" data-bs-toggle="tooltip">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="2"/>
                                            <path d="M22 12c-2.667 3.333 -6 5 -10 5s-7.333 -1.667 -10 -5c2.667 -3.333 6 -5 10 -5s7.333 1.667 10 5"/>
                                        </svg>
                                    </a>
                                </span>
                            </div>
                        </div>
                        <div class="mb-2">
                            <label class="form-check">
                                <input type="checkbox" class="form-check-input"/>
                                <span class="form-check-label">Remember me on this device</span>
                            </label>
                        </div>
                        <div class="form-footer">
                            <button type="submit" class="btn btn-primary w-100">Sign in</button>
                        </div>
                    </form>
                </div>
                <div class="hr-text">or</div>
                <div class="card-body">
                    <div class="row">
                        <div class="col">
                            <a href="register.php" class="btn w-100">
                                <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                    <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                    <circle cx="12" cy="7" r="4"/>
                                    <path d="M6 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                </svg>
                                Create new account
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Demo credentials -->
            <div class="text-center text-white-50 mt-3">
                <small>
                    Demo Customer: john_doe / user123
                </small>
            </div>

            <!-- Admin login link -->
            <div class="text-center mt-2">
                <a href="../admin/login.php" class="text-white-50 text-decoration-none">
                    <small>
                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-sm me-1" width="16" height="16" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                            <rect x="3" y="11" width="18" height="10" rx="2"/>
                            <circle cx="12" cy="16" r="1"/>
                            <path d="M7 11v-4a5 5 0 0 1 10 0v4"/>
                        </svg>
                        Admin Login
                    </small>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Show/hide password toggle
        document.querySelector('.input-group-text a').addEventListener('click', function(e) {
            e.preventDefault();
            const passwordInput = document.querySelector('input[name="password"]');
            const icon = this.querySelector('svg');
            
            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                icon.innerHTML = '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="3" y1="3" x2="21" y2="21"/><path d="M10.584 10.587a2 2 0 0 0 2.828 2.83"/><path d="M9.363 5.365a9.466 9.466 0 0 1 2.637 -.365c4 0 7.333 1.667 10 5c-.778 .956 -1.612 1.825 -2.503 2.601m-2.14 1.855a9.466 9.466 0 0 1 -5.357 1.544c-4 0 -7.333 -1.667 -10 -5a15.9 15.9 0 0 1 4.49 -5.102"/>';
            } else {
                passwordInput.type = 'password';
                icon.innerHTML = '<path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="2"/><path d="M22 12c-2.667 3.333 -6 5 -10 5s-7.333 -1.667 -10 -5c2.667 -3.333 6 -5 10 -5s7.333 1.667 10 5"/>';
            }
        });
    </script>
</body>
</html>
