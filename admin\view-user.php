<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'User Details';

$user_id = intval($_GET['id'] ?? 0);

if ($user_id <= 0) {
    redirect('users.php');
}

// Get user data
try {
    $db = getDB();
    $sql = "SELECT * FROM accounts WHERE id = ? AND is_admin = 0";
    $result = $db->query($sql, [$user_id]);
    
    if ($result->num_rows === 0) {
        setFlashMessage('error', 'User not found.');
        redirect('users.php');
    }
    
    $user = $result->fetch_assoc();
    
    // Get user's recent transactions
    $transactions_sql = "SELECT t.*, 
                               CASE 
                                   WHEN t.sender_id = ? THEN 'sent'
                                   ELSE 'received'
                               END as direction,
                               CASE 
                                   WHEN t.sender_id = ? THEN t.recipient_name
                                   ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                               END as other_party
                        FROM transfers t 
                        WHERE (t.sender_id = ? OR t.recipient_id = ?) 
                        ORDER BY t.created_at DESC 
                        LIMIT 10";
    
    $transactions_result = $db->query($transactions_sql, [$user_id, $user_id, $user_id, $user_id]);
    
    // Get user's beneficiaries
    $beneficiaries_sql = "SELECT * FROM beneficiaries WHERE user_id = ? ORDER BY name";
    $beneficiaries_result = $db->query($beneficiaries_sql, [$user_id]);
    
    // Get user's support tickets
    $tickets_sql = "SELECT * FROM tickets WHERE user_id = ? ORDER BY created_at DESC LIMIT 5";
    $tickets_result = $db->query($tickets_sql, [$user_id]);
    
    // Get transaction statistics
    $stats_sql = "SELECT 
                    COUNT(*) as total_transactions,
                    SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                    SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received,
                    COUNT(CASE WHEN sender_id = ? AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_sent_count,
                    COUNT(CASE WHEN recipient_id = ? AND DATE(created_at) >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN 1 END) as monthly_received_count
                  FROM transfers 
                  WHERE (sender_id = ? OR recipient_id = ?) AND status = 'completed'";
    
    $stats_result = $db->query($stats_sql, [$user_id, $user_id, $user_id, $user_id, $user_id, $user_id]);
    $stats = $stats_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("View user error: " . $e->getMessage());
    setFlashMessage('error', 'Error loading user data.');
    redirect('users.php');
}

include '../includes/admin_header.php';
?>



<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Admin</a></li>
                            <li class="breadcrumb-item"><a href="users.php">Users</a></li>
                            <li class="breadcrumb-item active" aria-current="page">User Details</li>
                        </ol>
                    </nav>
                    <div class="page-pretitle">
                        Administration
                    </div>
                    <h2 class="page-title">
                        <?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?>
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="edit-user.php?id=<?php echo $user_id; ?>" class="btn btn-primary">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <path d="M7 7h-1a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-1"/>
                                <path d="M20.385 6.585a2.1 2.1 0 0 0 -2.97 -2.97l-8.415 8.385v3h3l8.385 -8.415z"/>
                                <path d="M16 5l3 3"/>
                            </svg>
                            Edit User
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <!-- User Overview -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-md-6 col-lg-4">
                    <div class="card">
                        <div class="card-body text-center">
                            <span class="avatar avatar-xl mb-3"><?php echo strtoupper(substr($user['first_name'], 0, 1) . substr($user['last_name'], 0, 1)); ?></span>
                            <h3 class="m-0 mb-1"><?php echo htmlspecialchars($user['first_name'] . ' ' . $user['last_name']); ?></h3>
                            <div class="text-muted">@<?php echo htmlspecialchars($user['username']); ?></div>
                            <div class="mt-3">
                                <span class="badge bg-<?php echo $user['status'] === 'active' ? 'green' : ($user['status'] === 'suspended' ? 'red' : 'gray'); ?>-lt">
                                    <?php echo ucfirst($user['status']); ?>
                                </span>
                                <span class="badge bg-<?php echo $user['kyc_status'] === 'verified' ? 'green' : ($user['kyc_status'] === 'rejected' ? 'red' : 'yellow'); ?>-lt">
                                    KYC: <?php echo ucfirst($user['kyc_status']); ?>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Account Information</h3>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-5">Account Number:</dt>
                                        <dd class="col-7"><?php echo htmlspecialchars($user['account_number']); ?></dd>
                                        
                                        <dt class="col-5">Email:</dt>
                                        <dd class="col-7"><?php echo htmlspecialchars($user['email']); ?></dd>
                                        
                                        <dt class="col-5">Phone:</dt>
                                        <dd class="col-7"><?php echo htmlspecialchars($user['phone'] ?: 'Not provided'); ?></dd>
                                        
                                        <dt class="col-5">Account Type:</dt>
                                        <dd class="col-7"><?php echo ucfirst($user['account_type']); ?></dd>
                                    </dl>
                                </div>
                                <div class="col-md-6">
                                    <dl class="row">
                                        <dt class="col-5">Balance:</dt>
                                        <dd class="col-7"><strong><?php echo formatCurrency($user['balance']); ?></strong></dd>
                                        
                                        <dt class="col-5">Date of Birth:</dt>
                                        <dd class="col-7"><?php echo $user['date_of_birth'] ? formatDate($user['date_of_birth'], 'M d, Y') : 'Not provided'; ?></dd>
                                        
                                        <dt class="col-5">Joined:</dt>
                                        <dd class="col-7"><?php echo formatDate($user['created_at'], 'M d, Y'); ?></dd>
                                        
                                        <dt class="col-5">Last Login:</dt>
                                        <dd class="col-7"><?php echo $user['last_login'] ? formatDate($user['last_login'], 'M d, Y H:i') : 'Never'; ?></dd>
                                    </dl>
                                </div>
                            </div>
                            
                            <?php if (!empty($user['address'])): ?>
                            <div class="row">
                                <div class="col-12">
                                    <dl class="row">
                                        <dt class="col-2">Address:</dt>
                                        <dd class="col-10"><?php echo nl2br(htmlspecialchars($user['address'])); ?></dd>
                                    </dl>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistics -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Total Transactions</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-blue">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                            <polyline points="9 15 12 12 15 15"/>
                                            <polyline points="12 12 12 21"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($stats['total_transactions']); ?></div>
                            <div class="d-flex mb-2">
                                <div>All time</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Money Sent</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-red">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <polyline points="7 13 12 18 17 13"/>
                                            <polyline points="12 18 12 6"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo formatCurrency($stats['total_sent']); ?></div>
                            <div class="d-flex mb-2">
                                <div>Total sent</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Money Received</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <polyline points="17 11 12 6 7 11"/>
                                            <polyline points="12 6 12 18"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo formatCurrency($stats['total_received']); ?></div>
                            <div class="d-flex mb-2">
                                <div>Total received</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Monthly Activity</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-yellow">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <circle cx="12" cy="12" r="9"/>
                                            <polyline points="12 7 12 12 15 15"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo number_format($stats['monthly_sent_count'] + $stats['monthly_received_count']); ?></div>
                            <div class="d-flex mb-2">
                                <div>Last 30 days</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Transactions -->
            <div class="row row-deck row-cards">
                <div class="col-md-8">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Transactions</h3>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($transactions_result && $transactions_result->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($transaction = $transactions_result->fetch_assoc()): ?>
                                        <tr>
                                            <td class="text-muted">
                                                <?php echo formatDate($transaction['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $transaction['direction'] === 'sent' ? 'red' : 'green'; ?>-lt">
                                                    <?php echo ucfirst($transaction['direction']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div>
                                                    <?php echo $transaction['direction'] === 'sent' ? 'To: ' : 'From: '; ?>
                                                    <?php echo htmlspecialchars($transaction['other_party']); ?>
                                                </div>
                                                <?php if (!empty($transaction['description'])): ?>
                                                <div class="text-muted small">
                                                    <?php echo htmlspecialchars($transaction['description']); ?>
                                                </div>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-<?php echo $transaction['direction'] === 'sent' ? 'red' : 'green'; ?>">
                                                <?php echo ($transaction['direction'] === 'sent' ? '-' : '+') . formatCurrency($transaction['amount']); ?>
                                            </td>
                                            <td>
                                                <span class="status-<?php echo $transaction['status']; ?>">
                                                    <?php echo ucfirst($transaction['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No transactions yet</p>
                                <p class="empty-subtitle text-muted">
                                    This user hasn't made any transactions.
                                </p>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Beneficiaries and Support -->
                <div class="col-md-4">
                    <div class="card mb-3">
                        <div class="card-header">
                            <h3 class="card-title">Beneficiaries</h3>
                        </div>
                        <div class="card-body">
                            <?php if ($beneficiaries_result && $beneficiaries_result->num_rows > 0): ?>
                            <div class="list-group list-group-flush">
                                <?php while ($beneficiary = $beneficiaries_result->fetch_assoc()): ?>
                                <div class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col-auto">
                                            <span class="avatar avatar-sm"><?php echo strtoupper(substr($beneficiary['name'], 0, 2)); ?></span>
                                        </div>
                                        <div class="col text-truncate">
                                            <div class="text-reset d-block"><?php echo htmlspecialchars($beneficiary['name']); ?></div>
                                            <div class="d-block text-muted text-truncate mt-n1">
                                                <?php echo htmlspecialchars($beneficiary['account_number']); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>
                            <?php else: ?>
                            <div class="text-muted text-center">No beneficiaries added</div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Support Tickets</h3>
                        </div>
                        <div class="card-body">
                            <?php if ($tickets_result && $tickets_result->num_rows > 0): ?>
                            <div class="list-group list-group-flush">
                                <?php while ($ticket = $tickets_result->fetch_assoc()): ?>
                                <div class="list-group-item">
                                    <div class="row align-items-center">
                                        <div class="col text-truncate">
                                            <div class="text-reset d-block"><?php echo htmlspecialchars($ticket['subject']); ?></div>
                                            <div class="d-block text-muted text-truncate mt-n1">
                                                <span class="badge bg-<?php echo $ticket['status'] === 'open' ? 'red' : ($ticket['status'] === 'resolved' ? 'green' : 'yellow'); ?>-lt">
                                                    <?php echo ucfirst($ticket['status']); ?>
                                                </span>
                                                <?php echo formatDate($ticket['created_at'], 'M d'); ?>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endwhile; ?>
                            </div>
                            <?php else: ?>
                            <div class="text-muted text-center">No support tickets</div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
