<?php
require_once '../config/config.php';
requireLogin();

$page_title = 'Dashboard';

// Get user's recent transactions
try {
    $db = getDB();
    $sql = "SELECT t.*, 
                   CASE 
                       WHEN t.sender_id = ? THEN 'sent'
                       ELSE 'received'
                   END as direction,
                   CASE 
                       WHEN t.sender_id = ? THEN t.recipient_name
                       ELSE (SELECT CONCAT(first_name, ' ', last_name) FROM accounts WHERE id = t.sender_id)
                   END as other_party
            FROM transfers t 
            WHERE (t.sender_id = ? OR t.recipient_id = ?) 
            AND t.status = 'completed'
            ORDER BY t.created_at DESC 
            LIMIT 5";
    
    $user_id = $_SESSION['user_id'];
    $recent_transactions = $db->query($sql, [$user_id, $user_id, $user_id, $user_id]);
    
    // Get account balance (refresh from database)
    $balance_sql = "SELECT balance FROM accounts WHERE id = ?";
    $balance_result = $db->query($balance_sql, [$user_id]);
    $current_balance = $balance_result->fetch_assoc()['balance'];
    $_SESSION['balance'] = $current_balance; // Update session
    
    // Get monthly transaction summary
    $monthly_sql = "SELECT 
                        COUNT(*) as total_transactions,
                        SUM(CASE WHEN sender_id = ? THEN amount ELSE 0 END) as total_sent,
                        SUM(CASE WHEN recipient_id = ? THEN amount ELSE 0 END) as total_received
                    FROM transfers 
                    WHERE (sender_id = ? OR recipient_id = ?) 
                    AND status = 'completed'
                    AND MONTH(created_at) = MONTH(CURRENT_DATE())
                    AND YEAR(created_at) = YEAR(CURRENT_DATE())";
    
    $monthly_result = $db->query($monthly_sql, [$user_id, $user_id, $user_id, $user_id]);
    $monthly_stats = $monthly_result->fetch_assoc();
    
} catch (Exception $e) {
    error_log("Dashboard error: " . $e->getMessage());
    $recent_transactions = null;
    $monthly_stats = ['total_transactions' => 0, 'total_sent' => 0, 'total_received' => 0];
}

include '../includes/user_header.php';
?>

<?php include '../includes/sidebar.php'; ?>

<div class="page-wrapper">
    <!-- Page header -->
    <div class="page-header d-print-none">
        <div class="container-xl">
            <div class="row g-2 align-items-center">
                <div class="col">
                    <div class="page-pretitle">
                        Welcome back
                    </div>
                    <h2 class="page-title">
                        <?php echo htmlspecialchars($_SESSION['first_name'] . ' ' . $_SESSION['last_name']); ?>
                    </h2>
                </div>
                <div class="col-auto ms-auto d-print-none">
                    <div class="btn-list">
                        <a href="/transfers/" class="btn btn-primary d-none d-sm-inline-block">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                            Send Money
                        </a>
                        <a href="/transfers/" class="btn btn-primary d-sm-none btn-icon">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                <line x1="12" y1="5" x2="12" y2="19"/>
                                <line x1="5" y1="12" x2="19" y2="12"/>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Page body -->
    <div class="page-body">
        <div class="container-xl">
            <!-- Account Balance Card -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-12">
                    <div class="card balance-card">
                        <div class="card-body">
                            <div class="row align-items-center">
                                <div class="col">
                                    <div class="font-weight-medium text-white-50">
                                        Account Balance
                                    </div>
                                    <div class="h1 text-white mb-0">
                                        <?php echo formatCurrency($current_balance); ?>
                                    </div>
                                    <div class="text-white-50">
                                        <?php if (isset($_SESSION['is_admin']) && $_SESSION['is_admin']): ?>
                                            Admin Account
                                        <?php else: ?>
                                            Account: <?php echo htmlspecialchars($_SESSION['account_number'] ?? 'N/A'); ?>
                                        <?php endif; ?>
                                    </div>
                                </div>
                                <div class="col-auto">
                                    <div class="bg-white bg-opacity-25 rounded p-3">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon icon-lg text-white" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <path d="M17 8v-3a1 1 0 0 0 -1 -1h-10a2 2 0 0 0 0 4h12a1 1 0 0 1 1 1v3m0 4v3a1 1 0 0 1 -1 1h-12a2 2 0 0 1 -2 -2v-12"/>
                                            <path d="M20 12v4h-4a2 2 0 0 1 0 -4h4"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-sm-6 col-lg-3">
                    <div class="card quick-action-card" onclick="location.href='<?php echo url('transfers/'); ?>'">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Send Money</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="<?php echo url('transfers/'); ?>" data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M7 18a4.6 4.4 0 0 1 0 -9a5 4.5 0 0 1 11 2h1a3.5 3.5 0 0 1 0 7h-12"/>
                                                <polyline points="9 15 12 12 15 15"/>
                                                <polyline points="12 12 12 21"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3">Transfer</div>
                            <div class="d-flex mb-2">
                                <div>Quick and secure money transfers</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card quick-action-card" onclick="location.href='/transfers/history.php'">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Transaction History</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="/transfers/history.php">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <path d="M3 12a9 9 0 1 0 9 -9a9.75 9.75 0 0 0 -6.74 2.74l-2.26 2.26"/>
                                                <path d="M3 3v6h6"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo $monthly_stats['total_transactions']; ?></div>
                            <div class="d-flex mb-2">
                                <div>Transactions this month</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card quick-action-card" onclick="location.href='/transfers/beneficiaries.php'">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Beneficiaries</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="/transfers/beneficiaries.php">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <circle cx="9" cy="7" r="4"/>
                                                <path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/>
                                                <path d="M16 3.13a4 4 0 0 1 0 7.75"/>
                                                <path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3">Manage</div>
                            <div class="d-flex mb-2">
                                <div>Saved recipients</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-sm-6 col-lg-3">
                    <div class="card quick-action-card" onclick="location.href='/support/'">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Support</div>
                                <div class="ms-auto lh-1">
                                    <div class="dropdown">
                                        <a class="dropdown-toggle text-muted" href="/support/">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                                <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                                <circle cx="12" cy="12" r="4"/>
                                                <circle cx="12" cy="12" r="9"/>
                                                <line x1="15" y1="9" x2="9" y2="15"/>
                                                <line x1="9" y1="9" x2="15" y2="15"/>
                                            </svg>
                                        </a>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3">Help</div>
                            <div class="d-flex mb-2">
                                <div>Get assistance</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Monthly Summary -->
            <div class="row row-deck row-cards mb-3">
                <div class="col-md-6 col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Money Sent</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <polyline points="7 13 12 18 17 13"/>
                                            <polyline points="12 18 12 6"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo formatCurrency($monthly_stats['total_sent']); ?></div>
                            <div class="d-flex mb-2">
                                <div>This month</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6 col-lg-4">
                    <div class="card">
                        <div class="card-body">
                            <div class="d-flex align-items-center">
                                <div class="subheader">Money Received</div>
                                <div class="ms-auto lh-1">
                                    <div class="text-green">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <polyline points="17 11 12 6 7 11"/>
                                            <polyline points="12 6 12 18"/>
                                        </svg>
                                    </div>
                                </div>
                            </div>
                            <div class="h1 mb-3"><?php echo formatCurrency($monthly_stats['total_received']); ?></div>
                            <div class="d-flex mb-2">
                                <div>This month</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Recent Transactions -->
            <div class="row row-deck row-cards">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">Recent Transactions</h3>
                            <div class="card-actions">
                                <a href="/transfers/history.php" class="btn btn-outline-primary btn-sm">
                                    View All
                                </a>
                            </div>
                        </div>
                        <div class="card-body p-0">
                            <?php if ($recent_transactions && $recent_transactions->num_rows > 0): ?>
                            <div class="table-responsive">
                                <table class="table table-vcenter card-table">
                                    <thead>
                                        <tr>
                                            <th>Date</th>
                                            <th>Type</th>
                                            <th>Description</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php while ($transaction = $recent_transactions->fetch_assoc()): ?>
                                        <tr class="transaction-item">
                                            <td class="text-muted">
                                                <?php echo formatDate($transaction['created_at'], 'M d, Y'); ?>
                                            </td>
                                            <td>
                                                <span class="badge bg-<?php echo $transaction['direction'] === 'sent' ? 'red' : 'green'; ?>-lt">
                                                    <?php echo ucfirst($transaction['direction']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="d-flex py-1 align-items-center">
                                                    <div class="flex-fill">
                                                        <div class="font-weight-medium">
                                                            <?php echo $transaction['direction'] === 'sent' ? 'To: ' : 'From: '; ?>
                                                            <?php echo htmlspecialchars($transaction['other_party']); ?>
                                                        </div>
                                                        <?php if (!empty($transaction['description'])): ?>
                                                        <div class="text-muted">
                                                            <?php echo htmlspecialchars($transaction['description']); ?>
                                                        </div>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </td>
                                            <td class="text-<?php echo $transaction['direction'] === 'sent' ? 'red' : 'green'; ?>">
                                                <?php echo ($transaction['direction'] === 'sent' ? '-' : '+') . formatCurrency($transaction['amount']); ?>
                                            </td>
                                            <td>
                                                <span class="status-<?php echo $transaction['status']; ?>">
                                                    <?php echo ucfirst($transaction['status']); ?>
                                                </span>
                                            </td>
                                        </tr>
                                        <?php endwhile; ?>
                                    </tbody>
                                </table>
                            </div>
                            <?php else: ?>
                            <div class="empty">
                                <div class="empty-img">
                                    <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTI4IiBoZWlnaHQ9IjEyOCIgdmlld0JveD0iMCAwIDEyOCAxMjgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik02NCA5NkM4MC41Njg1IDk2IDk0IDgyLjU2ODUgOTQgNjZDOTQgNDkuNDMxNSA4MC41Njg1IDM2IDY0IDM2QzQ3LjQzMTUgMzYgMzQgNDkuNDMxNSAzNCA2NkMzNCA4Mi41Njg1IDQ3LjQzMTUgOTYgNjQgOTZaIiBzdHJva2U9IiNEQURERTIiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+CjxwYXRoIGQ9Ik02NCA2NlY1NCIgc3Ryb2tlPSIjREFEREUyIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNNjQgNzhWNzYiIHN0cm9rZT0iI0RBRERFRSI+PHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="" height="128" width="128">
                                </div>
                                <p class="empty-title">No transactions yet</p>
                                <p class="empty-subtitle text-muted">
                                    Start by sending money to someone or receiving a transfer.
                                </p>
                                <div class="empty-action">
                                    <a href="/transfers/" class="btn btn-primary">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round">
                                            <path stroke="none" d="M0 0h24v24H0z" fill="none"/>
                                            <line x1="12" y1="5" x2="12" y2="19"/>
                                            <line x1="5" y1="12" x2="19" y2="12"/>
                                        </svg>
                                        Send Money
                                    </a>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
